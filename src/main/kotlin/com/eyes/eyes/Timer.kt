package com.eyes.eyes

import com.intellij.openapi.Disposable
import com.intellij.openapi.components.Service
import kotlinx.coroutines.CoroutineScope
import java.util.UUID
import javax.swing.Timer

@Service(Service.Level.APP)
class Timer(
    private val cs: CoroutineScope,
) : Disposable {
    private var timer: Timer? = null
    private var timerInterval = 10_000

    init {
        startTimer()
    }

    private fun onTimerTick() {
        val id = UUID.randomUUID().toString()
        println("Ticking! ID: $id")
    }

    private fun startTimer() {
        timer =
            Timer(timerInterval) {
                onTimerTick()
            }
        timer?.start()
    }

    override fun dispose() {
    }
}
